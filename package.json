{"name": "skillwatch-app", "version": "5.3.1", "type": "module", "description": "Skillwatch-app", "scripts": {"watch": "vite --port 8080", "dist": "vite build", "dist-dev": "vite build", "preview": "vite preview", "clean": "rimraf dist coverage", "lint:css": "stylelint 'src/**/*.tsx'", "lint:ts": "eslint 'src/**/*.{ts,tsx}'", "lint": "eslint . --ext ts,tsx", "lint:fix": "eslint --fix . --ext ts,tsx", "test:update": "run-p test:types 'test:jest -- -u'", "test:watch": "run-p 'test:types -- -w' 'test:jest -- --watch'", "test:jest": "jest --config configs/jest/jest.config.cjs", "test:types": "tsc", "test": "run-p test:types test:jest", "dist:analyze": "npm run dist -- --analyze", "update": "ncu", "prepare": "husky install", "pre-commit": "lint-staged"}, "author": "Scalereal Technologies", "license": "ISC", "lint-staged": {"src/**/*.{ts,tsx}": ["eslint 'src/**/*.tsx' --fix", "stylelint 'src/**/*.tsx' --fix"]}, "babel": {"extends": "@medly/babel-config-react"}, "engines": {"npm": "please-use-yarn", "yarn": ">= 1.19.1"}, "eslintConfig": {"parser": "@typescript-eslint/parser", "extends": "@medly/react"}, "stylelint": {"extends": "@medly/stylelint-config"}, "prettier": "@medly/prettier-config", "dependencies": {"@babel/preset-env": "^7.22.9", "@babel/preset-react": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@medly-components/core": "^6.19.3", "@medly-components/forms": "^2.1.75", "@medly-components/layout": "^3.2.29", "@medly-components/loaders": "^1.6.10", "@medly-components/theme": "^5.2.0", "@medly/babel-config-react": "^0.6.0", "@nivo/bar": "^0.83.0", "@nivo/core": "^0.83.0", "@nivo/legends": "^0.83.0", "@nivo/pie": "^0.83.0", "@react-oauth/google": "^0.8.0", "@reduxjs/toolkit": "^1.8.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react-hooks": "^8.0.1", "@types/turndown": "^5.0.1", "aos": "^2.3.4", "axios": "^1.12.0", "dompurify": "^3.2.4", "gapi-script": "^1.2.0", "jest": "^29.6.2", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.6.2", "jest-styled-components": "^7.1.1", "jquery": "^3.7.1", "jwt-decode": "^3.1.2", "libphonenumber-js": "^1.10.28", "lodash.debounce": "^4.0.8", "lottie-react": "^2.3.1", "quill": "^1.3.7", "react": "17.0.2", "react-dom": "17.0.2", "react-dropzone": "^14.2.3", "react-helmet-async": "^2.0.5", "react-icons": "^4.7.1", "react-microsoft-login": "^2.0.1", "react-owl-carousel": "^2.3.3", "react-phone-input-2": "^2.15.1", "react-quilljs": "^1.3.3", "react-redux": "^8.0.4", "react-refresh": "^0.14.0", "react-router-dom": "6.3.0", "react-tooltip": "^5.28.0", "react-vertical-timeline-component": "^3.6.0", "react-virtualized": "^9.22.5", "redux": "^4.2.0", "redux-persist": "^6.0.0", "styled-components": "^6.0.0", "stylis": "^4.0.0", "swiper": "^11.1.4", "timezones-list": "^3.1.0", "ts-jest": "^29.2.5", "turndown": "^7.1.2", "use-react-router-breadcrumbs": "^4.0.1", "vite-plugin-svgr": "^4.3.0", "whatwg-fetch": "^3.6.17"}, "devDependencies": {"@medly/eslint-config-react": "^0.3.1", "@medly/prettier-config": "^0.1.1", "@medly/stylelint-config": "^0.2.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "12.1.5", "@types/aos": "^3.0.4", "@types/dompurify": "^2.4.0", "@types/lodash.debounce": "^4.0.9", "@types/quill": "^2.0.9", "@types/react": "17.0.2", "@types/react-dom": "17.0.2", "@types/react-lottie": "^1.2.6", "@types/react-redux": "^7.1.24", "@types/react-tooltip": "^4.2.4", "@types/react-vertical-timeline-component": "^3.3.3", "@types/redux-mock-store": "^1.0.3", "@vitejs/plugin-react": "^4.3.4", "axios-mock-adapter": "^1.21.5", "buffer": "^6.0.3", "husky": "^8.0.1", "lint-staged": "^13.0.3", "msw": "^1.2.3", "redux-devtools-extension": "^2.13.9", "redux-mock-store": "^1.5.4", "rimraf": "^3.0.2", "typescript": "^5.1.3", "vite": "^6.3.6"}, "overrides": {"@nivo/core": {"d3-color": ">=3.1.0"}, "@nivo/bar": {"d3-color": ">=3.1.0"}, "@medly/babel-config-react": {"js-yaml": ">=3.13.1"}, "@medly/stylelint-config": {"postcss": ">=3.13.1"}, "msw": {"cookie": ">=0.7.0"}, "lint-staged": {"micromatch": ">=4.0.8"}}, "resolutions": {"@types/react": "17.0.2", "@types/react-dom": "17.0.2", "d3-color": "^3.1.0", "js-yaml": "^3.13.1", "postcss": ">=3.13.1", "cookie": ">=0.7.0", "micromatch": ">=4.0.8"}}