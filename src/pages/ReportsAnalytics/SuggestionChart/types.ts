export type FeedbackCount = {
    positive: number;
    improvement: number;
    appreciation: number;
};

export type FeedbackPercentage = {
    positive: number;
    improvement: number;
    appreciation: number;
};

export type IFeedbackGraph = {
    analyticsFeedbackCount: FeedbackCount;
    analyticsFeedbackPercentage: FeedbackPercentage;
};

export type StatusColorsType = 'completedColor' | 'inProgressColor' | 'pendingColor';

export type GraphDataType = {
    label: string;
    id: string;
    value: number;
    color: string;
};

export type IBarGraphData = {
    keys: string[];
    data: { [key: string]: string | number }[];
};

export type IReviewStatusDataEntries = [string, { [key: string]: string | number } | unknown];

// export type CategoriesType = 'Positive' | 'Improvement' | 'Appreciation';

export interface Props {
    reviewCycleId: string;
}
export interface ReviewGraphKeys {
    [key: string]: string;
}

export type SuggestionCategoryLabel =
    | 'Workplace & Culture'
    | 'Policies & Processes'
    | 'Learning & Development'
    | 'Tools & Technology'
    | 'Client & Customer Experience'
    | 'Innovation & New Product Ideas'
    | 'Others';

export type SuggestionProgressLabel = 'Under Review' | 'In Progress' | 'Implemented' | 'Deferred';

export interface SuggestionCategoryItem {
    id: number;
    label: SuggestionCategoryLabel;
    count: number;
    percentage: number;
}

export interface SuggestionProgressItem {
    id: number;
    label: SuggestionProgressLabel;
    count: number;
}

export interface SuggestionAnalyticsResponse {
    suggestionCategory: SuggestionCategoryItem[];
    suggestionProgress: SuggestionProgressItem[];
}
