import { Loader } from '@components/reusableComponents';
import { ResponsiveBar } from '@nivo/bar';
import { ResponsivePie } from '@nivo/pie';
import { FC, memo, useCallback, useMemo } from 'react';
import {
    ChartContainer,
    Color,
    ColorScheme,
    EmptyStateContainer,
    InfoSection,
    SuggestionCategoryWrapper,
    SuggestionProgressWrapper,
    TileTitle,
    TypeText
} from './SuggestionChart.styled';
import { TooltipContainer } from '@components/reusableComponents/Charts/Chart.styled';
import { dataCategories, suggestionCategoryColors, suggestionProgressCategories, suggestionProgressColors } from './constants';
import { Props, SuggestionCategoryItem } from './types';
import { useSuggestionChart } from './useSuggestionChart';
import { StyledEmptyText } from '@common';

export const SuggestionChart: FC<Props> = memo(({ reviewCycleId }) => {
    const { suggestionCategory, suggestionProgress, isLoading } = useSuggestionChart(reviewCycleId);

    const hasProgressData = suggestionProgress && suggestionProgress.length > 0 && suggestionProgress.some(item => item.count > 0);

    const progressTooltip = useCallback(
        ({ value }: { value: number }) => <TooltipContainer>{`${value} ${value > 1 ? 'Suggestions' : 'Suggestion'}`}</TooltipContainer>,
        []
    );

    const renderProgressChart = () => {
        if (isLoading) {
            return <Loader />;
        }

        if (!hasProgressData) {
            return (
                <EmptyStateContainer style={{ marginTop: '-4rem' }}>
                    <StyledEmptyText>
                        {reviewCycleId
                            ? 'No Suggestion progress found for the selected review cycle.'
                            : 'Please select a review cycle to view details.'}
                    </StyledEmptyText>
                </EmptyStateContainer>
            );
        }

        return (
            <div style={{ height: '38rem' }}>
                <ResponsiveBar
                    data={suggestionProgress}
                    keys={['count']}
                    indexBy="label"
                    margin={{ top: 20, right: 0, bottom: 0, left: 0 }}
                    padding={0.55}
                    enableLabel={false}
                    axisLeft={null}
                    axisBottom={{
                        tickSize: 0,
                        tickPadding: 15
                    }}
                    colors={({ indexValue }) => suggestionProgressColors[indexValue as keyof typeof suggestionProgressColors]}
                    enableGridY={false}
                    tooltip={progressTooltip}
                />
            </div>
        );
    };

    return (
        <ChartContainer>
            <SuggestionCategoryWrapper>
                <TileTitle textVariant="h4">Suggestion Category</TileTitle>
                {isLoading ? <Loader /> : <SuggestionProgressPie reviewCycleId={reviewCycleId} data={suggestionCategory} />}
                <InfoSection>
                    {dataCategories.map((node: string) => (
                        <ColorScheme key={node}>
                            <Color bgColor={suggestionCategoryColors[node as keyof typeof suggestionCategoryColors]} />
                            <TypeText>{node}</TypeText>
                        </ColorScheme>
                    ))}
                </InfoSection>
            </SuggestionCategoryWrapper>
            <SuggestionProgressWrapper>
                <TileTitle textVariant="h4">Suggestion Progress</TileTitle>
                <div>{renderProgressChart()}</div>
                <InfoSection style={{ display: 'flex', justifyContent: 'start', marginTop: '4rem' }}>
                    {suggestionProgressCategories.map((status: string) => (
                        <ColorScheme key={status} style={{ marginTop: '1rem' }}>
                            <Color bgColor={suggestionProgressColors[status as keyof typeof suggestionProgressColors]} />
                            <TypeText>{status}</TypeText>
                        </ColorScheme>
                    ))}
                </InfoSection>
            </SuggestionProgressWrapper>
        </ChartContainer>
    );
});

SuggestionChart.displayName = 'SuggestionChart';

export const SuggestionProgressPie = ({ reviewCycleId, data }: { reviewCycleId: string; data: SuggestionCategoryItem[] }) => {
    const hasData = data && data.length > 0 && data.some(item => item.count > 0);

    const pieChartData = useMemo(
        () =>
            data
                .map(item => ({
                    id: item.label,
                    label: item.label,
                    value: item.count,
                    percentage: item.percentage,
                    color: suggestionCategoryColors[item.label as keyof typeof suggestionCategoryColors]
                }))
                ?.filter(item => item.value > 0),
        [data]
    );

    const customTooltip = useCallback(({ datum }: { datum: any }) => {
        return (
            <TooltipContainer>
                <strong>{datum.id}</strong>
                {datum.value} suggestions ({datum.data.percentage}%)
            </TooltipContainer>
        );
    }, []);

    if (!hasData) {
        return (
            <EmptyStateContainer style={{ width: '100%' }}>
                <StyledEmptyText>
                    {reviewCycleId
                        ? 'No Suggestion progress found for the selected review cycle.'
                        : 'Please select a review cycle to view details.'}
                </StyledEmptyText>
            </EmptyStateContainer>
        );
    }

    return (
        <ResponsivePie
            data={pieChartData}
            margin={{ top: 30, right: 30, bottom: 30, left: 30 }}
            innerRadius={0.6}
            padAngle={2}
            cornerRadius={1}
            activeOuterRadiusOffset={2}
            borderWidth={2}
            borderColor={{
                from: 'color',
                modifiers: [['darker', 0.1]]
            }}
            colors={pieChartData.map(item => item.color)}
            enableArcLinkLabels={false}
            enableArcLabels={true}
            arcLabelsSkipAngle={8}
            arcLabelsTextColor="#ffffff"
            arcLabel={d => String(d.data.value)}
            isInteractive={true}
            tooltip={customTooltip}
            theme={{
                labels: {
                    text: {
                        fontSize: 14,
                        fontWeight: 600
                    }
                }
            }}
            animate={true}
            motionConfig="gentle"
        />
    );
};
