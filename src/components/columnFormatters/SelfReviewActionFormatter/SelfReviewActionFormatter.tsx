import { ColumnActionText } from '@common';
import { routeConstants } from '@constants';
import { reviewStatusConstants } from '@constants/reviewStatusConstants';
import { TableColumnConfig, Text } from '@medly-components/core';
import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from '@slice';

export const SelfReviewActionFormatter: TableColumnConfig['component'] = ({ rowData }) => {
    const navigateTo = useNavigate();
    const userDetails = useAppSelector(state => state.user);
    const isReviewCycleActive = rowData?.isReviewCycleActive;

    const status = useMemo(() => {
        if (!rowData?.isSelfReviewActive && !rowData?.publish && !rowData?.draft) {
            return reviewStatusConstants.SelfReviewNotStarted;
        }
        // Use consistent property name for date passed check
        if (rowData?.isSelfReviewDatePassed && (!rowData?.publish || rowData?.draft)) {
            return reviewStatusConstants.SelfReviewDatePassed;
        }
        if (rowData?.isSelfReviewActive && !rowData?.draft && !rowData?.publish) {
            return reviewStatusConstants.SelfReviewPending;
        }
        if (rowData?.isSelfReviewActive && rowData?.draft && !rowData?.publish) {
            return reviewStatusConstants.SelfReviewInProgress;
        }
        return reviewStatusConstants.SelfReviewSubmitted;
    }, [rowData]);

    const actionToShow = useMemo(() => {
        // Only show actions when review cycle is active or when viewing published reviews
        if (!isReviewCycleActive && !rowData?.publish) {
            return 'N/A';
        }

        // If review is published, show View action
        if (rowData?.publish) return 'View';

        // If review cycle is not active, don't show add/edit actions
        if (!isReviewCycleActive) return 'N/A';

        // If self review period has passed and nothing is published, show N/A
        if (rowData?.isSelfReviewDatePassed && !rowData?.publish) {
            return 'N/A';
        }

        // If there's a draft (auto-save happened at least once), show Edit
        if (rowData?.draft) return 'Edit';

        // If self review is active or not started, show Add
        if (rowData?.isSelfReviewActive || (!rowData?.isSelfReviewActive && !rowData?.isSelfReviewDatePassed)) {
            return 'Add';
        }

        return 'N/A';
    }, [rowData, isReviewCycleActive]);

    const handleClick = () => {
        navigateTo(
            `${
                actionToShow !== 'View'
                    ? routeConstants.selfReviewPerformanceGuidelines
                    : `/performance-review/self-review/${rowData?.reviewCycle}/review-summary`
            }`,
            {
                state: {
                    ...rowData,
                    reviewFromId: userDetails.id,
                    reviewToId: userDetails.id,
                    action: actionToShow
                }
            }
        );
    };

    console.log(rowData, 'rowData');

    return isReviewCycleActive &&
        (status === reviewStatusConstants.SelfReviewPending ||
            status === reviewStatusConstants.SelfReviewInProgress ||
            status === reviewStatusConstants.SelfReviewNotStarted ||
            status === reviewStatusConstants.SelfReviewSubmitted) ? (
        <ColumnActionText onClick={handleClick}>{actionToShow}</ColumnActionText>
    ) : (
        <Text>N/A</Text>
    );
};
