import { ColumnActionText } from '@common';
import { routeConstants } from '@constants';
import { reviewStatusConstants } from '@constants/reviewStatusConstants';
import { TableColumnConfig, Text } from '@medly-components/core';
import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from '@slice';

export const SelfReviewActionFormatter: TableColumnConfig['component'] = ({ rowData }) => {
    const navigateTo = useNavigate();
    const userDetails = useAppSelector(state => state.user);
    const isReviewCycleActive = rowData?.isReviewCycleActive;

    const status = useMemo(() => {
        if (!rowData?.isSelfReviewActive && !rowData?.publish && !rowData?.draft) {
            return reviewStatusConstants.SelfReviewNotStarted;
        }
        if (rowData?.isSelfReviewDatePassed && (!rowData?.publish || rowData?.draft)) {
            return reviewStatusConstants.SelfReviewDatePassed;
        }
        if (rowData?.isSelfReviewActive && !rowData?.draft && !rowData?.publish) {
            return reviewStatusConstants.SelfReviewPending;
        }
        if (rowData?.isSelfReviewActive && rowData?.draft && !rowData?.publish) {
            return reviewStatusConstants.SelfReviewInProgress;
        }
        if (rowData?.selfReviewDatePassed) {
            return reviewStatusConstants.SelfReviewDatePassed;
        }
        return reviewStatusConstants.SelfReviewSubmitted;
    }, [rowData]);

    const handleClick = () => {
        navigateTo(
            `${
                actionToShow !== 'View'
                    ? routeConstants.selfReviewPerformanceGuidelines
                    : `/performance-review/self-review/${rowData?.reviewCycle}/review-summary`
            }`,

            {
                state: {
                    ...rowData,
                    reviewFromId: userDetails.id,
                    reviewToId: userDetails.id,
                    action: actionToShow
                }
            }
        );
    };

    console.log(rowData, 'rowData');

    const actionToShow = useMemo(() => {
        // draft will be true when the auto save happened atleast once
        if (!rowData?.isSelfReviewActive && !rowData?.isSelfReviewDatePassed) {
            if (rowData?.draft) return 'Edit';
            return 'Add draft';
        }
        // if the review is active and draft is tru, then we need to show edit
        if (rowData?.draft) return 'Edit';
        if (rowData?.publish) return 'View';
        if (rowData?.isSelfReviewActive) return 'Add';
        return 'N/A';
    }, [rowData]);

    return isReviewCycleActive &&
        (status === reviewStatusConstants.SelfReviewPending ||
            status === reviewStatusConstants.SelfReviewInProgress ||
            status === reviewStatusConstants.SelfReviewNotStarted ||
            status === reviewStatusConstants.SelfReviewSubmitted) ? (
        <ColumnActionText onClick={handleClick}>{actionToShow}</ColumnActionText>
    ) : (
        <Text>N/A</Text>
    );
};
