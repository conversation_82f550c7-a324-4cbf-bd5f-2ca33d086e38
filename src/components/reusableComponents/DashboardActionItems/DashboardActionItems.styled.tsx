import { Chip, Text } from '@medly-components/core';
import { ProgressId } from '@pages/ViewPreviousGoals/types';
import styled from 'styled-components';
import { SwiperSlide } from 'swiper/react';

export const ActionItem = styled.div`
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    column-gap: 1rem;
    align-items: flex-start;

    ${Text.Style} {
        font-size: 1.4rem;
    }

    &:hover {
        ${Text.Style} {
            color: ${({ theme }) => theme.colors.blue[500]};
        }
    }
`;

export const ActionItemsDiv = styled.div`
    gap: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 2rem;
`;

export const NoGoalsDiv = styled.div`
    min-height: 32rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background-color: rgb(235 241 250 / 40%);
`;

export const StyledText = styled(Text)`
    display: flex;
    align-items: flex-start;

    svg {
        margin-top: 0;
    }
`;

export const StyledChip = styled(Chip)<{ progressId: ProgressId }>`
    border: white;
    margin: 0;
    padding: 0.5rem;
    min-width: 100px;
    ${Text.Style} {
        font-size: 1.1rem !important;
        color: #fff;
    }
    background-color: ${({ theme, progressId }) =>
        progressId === 1
            ? theme.customColors.ToDoColor //gray
            : progressId === 2
            ? theme.customColors.inProgressColor //yellowish-orange
            : progressId === 3
            ? theme.customColors.completedColor //green
            : theme.colors.yellow[700]};
`;

export const StyledSwiperSlide = styled(SwiperSlide)`
    height: 100%;
    display: flex;
    margin-bottom: 1rem;
`;

export const GoalsCardContainer = styled.div`
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    width: 100%;
`;

export const DashboardGoalsDiv = styled.div`
    display: flex;
    gap: 2rem;
`;

export const GoalsLeftSection = styled.div`
    flex: 1;
    padding: 1rem;
`;

export const GoalsLeftTitle = styled.div`
    font-weight: 600;
    font-size: 16px;
    color: #333;
    margin-bottom: 2rem;
    margin-top: 0.6rem;
    padding-left: 1rem;
`;

export const GoalsRightSection = styled.div`
    flex: 1;
    padding: 1rem;
`;

export const GoalsRightTitle = styled.div`
    font-weight: 600;
    font-size: 16px;
    color: #333;
    margin-bottom: 2rem;
    margin-top: 0.6rem;
    padding-left: 1rem;
`;

export const PieChartWrapper = styled.div`
    height: 35rem;
    width: 100%;
    position: relative;
    margin-top: 0;
    margin-bottom: 0;
`;
